import { FaTrashAlt } from "react-icons/fa";

const LineItem = ({ item, handleCheck, handleDelete }) => {
  return (
    <li>
      <input
        type="checkbox"
        checked={item.checked}
        onChange={() => handleCheck(item.id)}
        id={`item-${item.id}`}
      />
      <label
        htmlFor={`item-${item.id}`}
        className={item.checked ? "checked" : ""}
        onDoubleClick={() => handleCheck(item.id)}
      >
        {item.item}
      </label>
      <FaTrashAlt
        className="delete-btn"
        onClick={() => handleDelete(item.id)}
        role="button"
        tabIndex={0}
        aria-label={`Delete ${item.item}`}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            handleDelete(item.id);
          }
        }}
      />
    </li>
  );
};

export default LineItem;
