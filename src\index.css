/* CSS Custom Properties for Modern Design System */
:root {
  /* Colors */
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #a5b4fc;
  --secondary-color: #10b981;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --warning-color: #f59e0b;

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* Typography */
  --font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  min-height: 100vh;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-800);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* App Container */
.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 600px;
  margin: 0 auto;
  background: var(--white);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-xl);
  overflow: hidden;
  position: relative;
}

@media (max-width: 640px) {
  .App {
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
  }

  header {
    padding: var(--space-md) var(--space-lg);
  }

  header h1 {
    font-size: var(--font-size-xl);
  }

  main {
    padding: var(--space-lg);
  }

  li {
    padding: var(--space-md);
    gap: var(--space-sm);
  }

  label {
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 480px) {
  main {
    padding: var(--space-md);
  }

  li {
    padding: var(--space-sm) var(--space-md);
  }

  .empty-state {
    padding: var(--space-xl);
  }
}

/* Header Styles */
header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: var(--space-lg) var(--space-xl);
  text-align: center;
  position: relative;
  overflow: hidden;
}

header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

header h1 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  position: relative;
  z-index: 1;
  margin: 0;
  letter-spacing: -0.025em;
}

/* Main Content */
main {
  flex: 1;
  padding: var(--space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

/* List Styles */
ul {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

li {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

li:hover {
  border-color: var(--primary-light);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

li:has(input:checked) {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  border-color: var(--secondary-color);
}

li:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

/* Checkbox Styles */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  background: var(--white);
  cursor: pointer;
  position: relative;
  transition: all var(--transition-fast);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

input[type="checkbox"]:checked {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
}

input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-size: var(--font-size-xs);
  font-weight: bold;
}

input[type="checkbox"]:focus {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

/* Label Styles */
label {
  flex: 1;
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
}

label.checked {
  text-decoration: line-through;
  color: var(--gray-400);
}

/* Delete Button */
.delete-btn {
  color: var(--gray-400);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  min-height: 40px;
}

.delete-btn:hover,
.delete-btn:focus {
  color: var(--danger-color);
  background: var(--danger-color);
  color: var(--white);
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.delete-btn:focus {
  outline: 2px solid var(--danger-color);
  outline-offset: 2px;
}

.delete-btn:active {
  transform: scale(0.95);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-2xl);
  color: var(--gray-500);
}

.empty-state p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-md);
}

.empty-state::before {
  content: '📝';
  font-size: var(--font-size-3xl);
  display: block;
  margin-bottom: var(--space-md);
}

/* AddItem Component Styles */
.add-item-container {
  padding: var(--space-xl);
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
}

.add-item-form {
  display: flex;
  gap: var(--space-md);
  align-items: flex-end;
  max-width: 100%;
}

.add-item-form label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-xs);
}

.add-item-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.add-item-input {
  width: 100%;
  padding: var(--space-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  background: var(--white);
  color: var(--gray-800);
  transition: all var(--transition-fast);
  outline: none;
  box-shadow: var(--shadow-sm);
}

.add-item-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  transform: translateY(-1px);
}

.add-item-input:hover {
  border-color: var(--gray-400);
  box-shadow: var(--shadow-md);
}

.add-item-input::placeholder {
  color: var(--gray-400);
  font-style: italic;
}

.add-item-input:invalid:not(:placeholder-shown) {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.add-item-input:valid:not(:placeholder-shown) {
  border-color: var(--secondary-color);
}

.add-item-submit-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-md);
  min-width: 48px;
  min-height: 48px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.add-item-submit-btn:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.add-item-submit-btn:focus {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

.add-item-submit-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.add-item-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.add-item-submit-btn:hover::before {
  left: 100%;
}

.add-item-submit-btn:disabled {
  background: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.add-item-submit-btn:disabled::before {
  display: none;
}

/* Responsive adjustments for AddItem */
@media (max-width: 640px) {
  .add-item-container {
    padding: var(--space-lg);
  }

  .add-item-form {
    gap: var(--space-sm);
  }

  .add-item-input {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-sm);
  }

  .add-item-submit-btn {
    min-width: 44px;
    min-height: 44px;
    padding: var(--space-sm);
  }
}

@media (max-width: 480px) {
  .add-item-container {
    padding: var(--space-md);
  }

  .add-item-form {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-md);
  }

  .add-item-submit-btn {
    align-self: flex-end;
    min-width: 60px;
  }
}

/* Footer */
footer {
  background: var(--gray-800);
  color: var(--gray-300);
  padding: var(--space-lg);
  text-align: center;
  font-size: var(--font-size-sm);
  border-top: 1px solid var(--gray-200);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

li {
  animation: slideIn var(--transition-normal) ease-out;
}

.App {
  animation: fadeIn var(--transition-slow) ease-out;
}

input[type="checkbox"]:checked {
  animation: bounceIn 0.3s ease-out;
}

.empty-state {
  animation: fadeIn var(--transition-slow) ease-out;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}