import { FaPlus } from "react-icons/fa";

const AddItem = ({ newItem, setNewItem, handleSubmit }) => {
  return (
    <div className="add-item-container" onSubmit={handleSubmit}>
      <form className="add-item-form">
        <div className="add-item-input-group">
          <label htmlFor="addItem">Add New Item</label>
          <input
            type="text"
            autoFocus
            id="addItem"
            className="add-item-input"
            placeholder="Enter grocery item..."
            required
            value={newItem}
            onChange={(e) => setNewItem(e.target.value)}
          />
        </div>
        <button
          type="submit"
          className="add-item-submit-btn"
          aria-label="Add Item"
          disabled={!newItem || !newItem.trim()}
        >
          <FaPlus />
        </button>
      </form>
    </div>
  );
};

export default AddItem;
